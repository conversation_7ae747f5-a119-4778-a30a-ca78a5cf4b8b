package com.superhexa.supervision.feature.miwear.speechhub.service

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import androidx.core.app.NotificationCompat
import com.superhexa.supervision.feature.miwear.speechhub.R
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.db.DbHelper
import com.superhexa.supervision.library.db.bean.MediaBean
import com.xiaomi.ai.capability.request.model.Phrase
import com.xiaomi.aivs.track.RecordEventParams
import io.objectbox.query.QueryBuilder
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import timber.log.Timber
import java.io.File

/**
 * 转写通知管理器
 * 负责在转写完成时发送通知给用户
 */
@Suppress("TooGenericExceptionCaught", "MaxLineLength")
class TranscriptionNotificationManager private constructor() : TranscriptionStateListener {

    companion object {
        private const val CHANNEL_ID = "transcription_channel"
        private const val CHANNEL_NAME = "转写通知"
        private const val CHANNEL_DESCRIPTION = "音频转写完成通知"
        private const val NOTIFICATION_ID_BASE = 10000

        @Volatile
        private var INSTANCE: TranscriptionNotificationManager? = null

        fun getInstance(): TranscriptionNotificationManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: TranscriptionNotificationManager().also { INSTANCE = it }
            }
        }
    }

    private val context: Context = LibBaseApplication.instance
    private val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

    // 跟踪当前在前台的音频文件路径，如果用户在转写界面则不发送通知
    private var currentForegroundFilePath: String? = null

    // 协程作用域，用于异步查询数据库
    private val coroutineScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)

    init {
        createNotificationChannel()
        // 注册到后台转写服务
        RecordAudioService.getInstance().registerListener(this)
        MMKVUtils.encode(RecordEventParams.EVENT_RECORD_CURRENT_NODE, 0)
        Timber.i("转写通知管理器初始化完成")
    }

    /**
     * 设置当前在前台的音频文件路径
     * 当用户在转写界面时调用，避免发送不必要的通知
     */
    fun setForegroundFilePath(filePath: String?) {
        currentForegroundFilePath = filePath
        Timber.i("设置前台文件路径: $filePath")
    }

    /**
     * 创建通知渠道（Android 8.0+）
     */
    private fun createNotificationChannel() {
        val channel = NotificationChannel(
            CHANNEL_ID,
            CHANNEL_NAME,
            NotificationManager.IMPORTANCE_DEFAULT
        ).apply {
            description = CHANNEL_DESCRIPTION
            setShowBadge(true)
        }
        notificationManager.createNotificationChannel(channel)
        Timber.i("创建转写通知渠道")
    }

    /**
     * 发送转写完成通知
     */
    private fun sendTranscriptionCompletedNotification(filePath: String, phrases: List<Phrase>) {
        // 如果用户当前在转写界面查看这个文件，则不发送通知
        if (currentForegroundFilePath == filePath) {
            Timber.i("用户在前台查看该文件，跳过通知: $filePath")
            return
        }

        val fileName = File(filePath).nameWithoutExtension
        val title = "小米眼镜"
        // 修改通知内容为指定文本
        val content = "录音转写已生成，点击查看"

        // 创建点击通知的意图（这里需要根据实际的Activity来设置）
        val intent = createOpenTranscriptionIntent(filePath)
        val pendingIntent = PendingIntent.getActivity(
            context,
            filePath.hashCode(),
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val notification = NotificationCompat.Builder(context, CHANNEL_ID)
            .setSmallIcon(R.drawable.record_icon)
            .setContentTitle(title)
            .setContentText(content)
            .setStyle(NotificationCompat.BigTextStyle().bigText(content))
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .build()

        val notificationId = NOTIFICATION_ID_BASE + filePath.hashCode()
        notificationManager.notify(notificationId, notification)

        Timber.i("发送转写完成通知: $fileName")
    }

    /**
     * 发送转写失败通知
     */
    private fun sendTranscriptionFailedNotification(filePath: String, errorCode: Int, isReTranscribe: Boolean = false) {
        // 如果用户当前在转写界面查看这个文件，则不发送通知
        if (currentForegroundFilePath == filePath) {
            Timber.i("用户在前台查看该文件，跳过失败通知: $filePath")
            return
        }

        val fileName = File(filePath).nameWithoutExtension
        val title = "小米眼镜"
        // 根据是否为重新转写设置不同的通知内容
        val content = if (isReTranscribe) {
            "录音转写生成失败，点击查看"
        } else {
            "录音转写与总结生成失败，点击查看"
        }

        val intent = createOpenTranscriptionIntent(filePath)
        val pendingIntent = PendingIntent.getActivity(
            context,
            filePath.hashCode(),
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val notification = NotificationCompat.Builder(context, CHANNEL_ID)
            .setSmallIcon(R.drawable.record_icon)
            .setContentTitle(title)
            .setContentText(content)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .build()

        val notificationId = NOTIFICATION_ID_BASE + filePath.hashCode()
        notificationManager.notify(notificationId, notification)

        Timber.i("发送转写失败通知: $fileName")
    }

    /**
     * 发送上传失败通知
     */
    private fun sendUploadFailedNotification(filePath: String, errorCode: Int) {
        // 如果用户当前在转写界面查看这个文件，则不发送通知
        if (currentForegroundFilePath == filePath) {
            Timber.i("用户在前台查看该文件，跳过上传失败通知: $filePath")
            // return
        }

        val fileName = File(filePath).nameWithoutExtension
        val title = "小米眼镜"
        val content = "录音上传失败，点击查看"

        val intent = createOpenTranscriptionIntent(filePath)
        val pendingIntent = PendingIntent.getActivity(
            context,
            filePath.hashCode(),
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val notification = NotificationCompat.Builder(context, CHANNEL_ID)
            .setSmallIcon(R.drawable.record_icon)
            .setContentTitle(title)
            .setContentText(content)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .build()

        val notificationId = NOTIFICATION_ID_BASE + filePath.hashCode()
        notificationManager.notify(notificationId, notification)

        Timber.i("发送上传失败通知: $fileName")
    }

    /**
     * 创建打开转写界面的意图
     * 根据文件路径查找对应的MediaBean，然后跳转到AudioTranscriptionFragment
     */
    private fun createOpenTranscriptionIntent(filePath: String): Intent {
        return createOpenTranscriptionIntentWithTab(filePath, null)
    }

    /**
     * 创建打开总结Tab的意图
     * 根据文件路径查找对应的MediaBean，然后跳转到AudioTranscriptionFragment的总结Tab
     */
    private fun createOpenSummaryTabIntent(filePath: String): Intent {
        return createOpenTranscriptionIntentWithTab(filePath, "Summary")
    }

    /**
     * 创建打开转写界面的意图，支持指定初始Tab
     * 根据文件路径查找对应的MediaBean，然后跳转到AudioTranscriptionFragment
     */
    private fun createOpenTranscriptionIntentWithTab(filePath: String, initialTab: String?): Intent {
        val intent = Intent(context, getMainActivityClass())
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP

        // 同步查找MediaBean并设置到Intent中
        try {
            val mediaBean = findMediaBeanByPathSync(filePath)
            if (mediaBean != null) {
                // 将MediaBean信息存储到Intent中，供Activity处理
                intent.putExtra("transcription_file_path", filePath)
                intent.putExtra("transcription_media_id", mediaBean.id)
                intent.putExtra("transcription_user_id", mediaBean.useId)
                // 添加初始Tab参数
                if (initialTab != null) {
                    intent.putExtra("transcription_initial_tab", initialTab)
                }
                Timber.i("找到对应的MediaBean，准备跳转: id=${mediaBean.id}, path=${mediaBean.path}, initialTab=$initialTab")
            } else {
                Timber.w("未找到对应的MediaBean: $filePath")
                // 即使没找到MediaBean，也传递文件路径，让Activity尝试处理
                intent.putExtra("transcription_file_path", filePath)
                if (initialTab != null) {
                    intent.putExtra("transcription_initial_tab", initialTab)
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "查找MediaBean失败: $filePath")
            // 发生异常时也传递文件路径
            intent.putExtra("transcription_file_path", filePath)
            if (initialTab != null) {
                intent.putExtra("transcription_initial_tab", initialTab)
            }
        }

        return intent
    }

    /**
     * 根据文件路径同步查找对应的MediaBean
     */
    private fun findMediaBeanByPathSync(filePath: String): MediaBean? {
        return try {
            // 首先尝试从转写数据库中查找
            val transcriptionBoxFor = DbHelper.getBoxStore().boxFor(com.superhexa.supervision.library.db.bean.AudioTranscriptionBean::class.java)
            val transcriptionBeans = transcriptionBoxFor.query()
                .equal(com.superhexa.supervision.library.db.bean.AudioTranscriptionBean_.path, filePath, QueryBuilder.StringOrder.CASE_SENSITIVE)
                .build()
                .find()

            if (transcriptionBeans.isNotEmpty()) {
                val transcriptionBean = transcriptionBeans.first()
                // 根据转写记录查找对应的MediaBean
                val mediaBoxFor = DbHelper.getBoxStore().boxFor(MediaBean::class.java)
                val mediaBean = mediaBoxFor.query()
                    .equal(com.superhexa.supervision.library.db.bean.MediaBean_.id, transcriptionBean.id.toLong())
                    .and()
                    .equal(com.superhexa.supervision.library.db.bean.MediaBean_.path, transcriptionBean.path, QueryBuilder.StringOrder.CASE_SENSITIVE)
                    .and()
                    .equal(com.superhexa.supervision.library.db.bean.MediaBean_.useId, transcriptionBean.userId, QueryBuilder.StringOrder.CASE_SENSITIVE)
                    .build()
                    .findFirst()

                return mediaBean
            }

            // 如果转写数据库中没有，直接从MediaBean数据库中按路径查找
            val mediaBoxFor = DbHelper.getBoxStore().boxFor(MediaBean::class.java)
            mediaBoxFor.query()
                .equal(com.superhexa.supervision.library.db.bean.MediaBean_.path, filePath, QueryBuilder.StringOrder.CASE_SENSITIVE)
                .build()
                .findFirst()
        } catch (e: Exception) {
            Timber.e(e, "查找MediaBean失败: $filePath")
            null
        }
    }

    /**
     * 获取主Activity的Class
     * 这里需要根据实际的主Activity来返回
     */
    private fun getMainActivityClass(): Class<*> {
        return try {
            // 尝试获取NavHostActivity
            Class.forName("com.superhexa.supervision.NavHostActivity")
        } catch (e: ClassNotFoundException) {
            try {
                // 如果找不到，尝试获取MainActivity
                Class.forName("com.superhexa.supervision.MainActivity")
            } catch (e2: ClassNotFoundException) {
                // 如果都找不到，使用默认的Activity类
                Timber.e("找不到主Activity类，使用默认Activity")
                android.app.Activity::class.java
            }
        }
    }

    // ========== TranscriptionStateListener 接口实现 ==========

    override fun onTranscriptionStarted(taskId: String, filePath: String) {
        // 转写开始时不需要发送通知
        Timber.i("转写开始通知: taskId=$taskId, filePath=$filePath")
    }

    override fun onTranscriptionSuccess(
        taskId: String,
        fileId: String,
        filePath: String,
        phrases: List<Phrase>,
        isDistinguishSpeakers: Boolean?
    ) {
        Timber.i("转写完成通知: taskId=$taskId, filePath=$filePath")
        sendTranscriptionCompletedNotification(filePath, phrases)
    }

    override fun onTranscriptionFailed(taskId: String, filePath: String, errorCode: Int, errorMessage: String?, isReTranscribe: Boolean) {
        Timber.i("转写失败通知: taskId=$taskId, filePath=$filePath, errorCode=$errorCode, isReTranscribe=$isReTranscribe")
        sendTranscriptionFailedNotification(filePath, errorCode, isReTranscribe)
    }

    override fun onPollingStatusChanged(taskId: String, filePath: String, isPolling: Boolean) {
        // 轮询状态变化不需要发送通知
        Timber.i("轮询状态变化通知: taskId=$taskId, filePath=$filePath, isPolling=$isPolling")
    }

    // ========== 录音上传相关回调方法 ==========

    override fun onUploadStarted(taskId: String, filePath: String) {
        // 上传开始不需要发送通知
        Timber.i("上传开始通知: taskId=$taskId, filePath=$filePath")
    }

    override fun onUploadSuccess(taskId: String, filePath: String, fileId: String?) {
        // 上传成功不需要发送通知
        Timber.i("上传成功通知: taskId=$taskId, filePath=$filePath, fileId=$filePath")
    }

    override fun onUploadFailed(taskId: String, filePath: String, errorCode: Int, errorMessage: String?) {
        Timber.e("上传失败通知: taskId=$taskId, filePath=$filePath, errorCode=$errorCode, errorMessage=$errorMessage")
        // 发送上传失败通知
        sendUploadFailedNotification(filePath, errorCode)
    }

    // ========== 录音总结相关回调方法 ==========

    override fun onSummaryStarted(taskId: String, filePath: String, summaryTaskId: String, template: String) {
        Timber.i("总结开始通知: taskId=$taskId, filePath=$filePath, summaryTaskId=$summaryTaskId")
        // 总结开始不需要发送通知
    }

    override fun onSummarySuccess(
        taskId: String,
        filePath: String,
        summaryTaskId: String,
        title: String,
        content: String,
        focusSpeakerSummaryContent: String?,
        template: String,
        isReSummary: Boolean
    ) {
        Timber.i("总结完成通知: taskId=$taskId, filePath=$filePath, summaryTaskId=$summaryTaskId, title=$title, isReSummary=$isReSummary")

        // 如果用户当前在转写界面查看这个文件，则不发送通知
        if (currentForegroundFilePath == filePath) {
            Timber.i("用户当前在转写界面，不发送总结完成通知: $filePath")
            return
        }

        // 发送总结完成通知
        sendSummaryCompletedNotification(filePath, title, content, template, isReSummary)
    }

    override fun onSummaryFailed(taskId: String, filePath: String, summaryTaskId: String, errorCode: Int, template: String, isReSummary: Boolean) {
        Timber.e("总结失败通知: taskId=$taskId, filePath=$filePath, summaryTaskId=$summaryTaskId, errorCode=$errorCode, isReSummary=$isReSummary")

        // 如果用户当前在转写界面查看这个文件，则不发送通知
        if (currentForegroundFilePath == filePath) {
            Timber.i("用户当前在转写界面，不发送总结失败通知: $filePath")
            return
        }

        // 发送总结失败通知
        sendSummaryFailedNotification(filePath, errorCode, template, isReSummary)
    }

    /**
     * 发送总结完成通知
     */
    private fun sendSummaryCompletedNotification(filePath: String, title: String, content: String, template: String, isReSummary: Boolean = false) {
        try {
            val fileName = File(filePath).nameWithoutExtension
            val notificationTitle = "小米眼镜"
            // 根据是否为重新总结设置不同的通知内容
            val notificationContent = if (isReSummary) {
                "录音总结已生成，点击查看"
            } else {
                "录音转写与总结已生成，点击查看"
            }

            val intent = createOpenSummaryTabIntent(filePath)
            val pendingIntent = PendingIntent.getActivity(
                context,
                filePath.hashCode(),
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            val notification = NotificationCompat.Builder(context, CHANNEL_ID)
                .setSmallIcon(R.drawable.record_icon)
                .setContentTitle(notificationTitle)
                .setContentText(notificationContent)
                .setContentIntent(pendingIntent)
                .setAutoCancel(true)
                .setPriority(NotificationCompat.PRIORITY_DEFAULT)
                .build()

            val notificationId = NOTIFICATION_ID_BASE + filePath.hashCode()
            notificationManager.notify(notificationId, notification)

            Timber.i("发送总结完成通知: $notificationTitle - $notificationContent")
        } catch (e: Exception) {
            Timber.e(e, "发送总结完成通知失败: $filePath")
        }
    }

    /**
     * 发送总结失败通知
     */
    private fun sendSummaryFailedNotification(filePath: String, errorCode: Int, template: String, isReSummary: Boolean = false) {
        try {
            val fileName = File(filePath).nameWithoutExtension
            val notificationTitle = "小米眼镜"
            // 根据是否为重新总结设置不同的通知内容
            val notificationContent = if (isReSummary) {
                "录音总结生成失败，点击查看"
            } else {
                "录音转写与总结已生成，点击查看"
            }

            val intent = createOpenTranscriptionIntent(filePath)
            val pendingIntent = PendingIntent.getActivity(
                context,
                filePath.hashCode(),
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            val notification = NotificationCompat.Builder(context, CHANNEL_ID)
                .setSmallIcon(R.drawable.record_icon)
                .setContentTitle(notificationTitle)
                .setContentText(notificationContent)
                .setContentIntent(pendingIntent)
                .setAutoCancel(true)
                .setPriority(NotificationCompat.PRIORITY_DEFAULT)
                .build()

            val notificationId = NOTIFICATION_ID_BASE + filePath.hashCode()
            notificationManager.notify(notificationId, notification)

            Timber.i("发送总结失败通知: $notificationTitle - $notificationContent")
        } catch (e: Exception) {
            Timber.e(e, "发送总结失败通知失败: $filePath")
        }
    }

    /**
     * 销毁通知管理器
     */
    fun destroy() {
        RecordAudioService.getInstance().unregisterListener(this)
        Timber.i("转写通知管理器销毁")
    }
}
