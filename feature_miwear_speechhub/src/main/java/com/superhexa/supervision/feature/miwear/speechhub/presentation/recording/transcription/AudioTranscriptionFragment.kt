package com.superhexa.supervision.feature.miwear.speechhub.presentation.recording.transcription

import android.content.Context
import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.alibaba.android.arouter.facade.annotation.Route
import com.alibaba.android.arouter.utils.TextUtils
import com.github.fragivity.navigator
import com.superhexa.supervision.feature.miwear.speechhub.compont.AudioTranscriptionScreen
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey.MIWEAR_RECORD_TRANSCRIPTION_FRAGMENT
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.extension.getBinderContent
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import com.superhexa.supervision.library.db.AudioTranscriptionDbHelper
import com.superhexa.supervision.library.db.bean.AudioTranscriptionBean
import com.superhexa.supervision.library.db.bean.MediaBean
import com.superhexa.supervision.library.db.bean.SummaryStatus
import com.superhexa.supervision.library.db.bean.TranscriptionStatus
import com.superhexa.supervision.library.statistic.O95Statistic
import com.xiaomi.ai.capability.request.model.Phrase
import com.xiaomi.wearable.core.gson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.kodein.di.generic.instance
import timber.log.Timber

/**
 * 类描述:音频转录、总结
 * 创建日期: 2025/3/14 on 15:43
 * 作者: GeYaoXiang
 */
@Route(path = MIWEAR_RECORD_TRANSCRIPTION_FRAGMENT)
class AudioTranscriptionFragment : BaseComposeFragment() {
    private val viewModel by instance<AudioTranscriptionViewModel>()
    private var currentMediaBean: MediaBean? = null
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        arguments?.getBinderContent<MediaBean>(BundleKey.Record)?.let { bean ->
            currentMediaBean = bean
            viewModel.run {
                initializeState(bean)
                loadAudioTranscriptionData(bean)

                // 处理初始Tab参数
                val initialTab = arguments?.getString("initial_tab")
                if (initialTab == "Summary") {
                    // 设置初始Tab为总结
                    setInitialTab(AudioTranscriptionViewModel.Tab.Summary)
                    Timber.i("设置初始Tab为总结")
                }
            }
            Timber.d("onViewCreated bean: $bean")
        }
        O95Statistic.exposeRecordTranscribeSummary()
    }

    private fun AudioTranscriptionViewModel.initializeState(bean: MediaBean) {
        mState.value.currentItem = bean
    }

    private fun AudioTranscriptionViewModel.loadAudioTranscriptionData(bean: MediaBean) {
        lifecycleScope.launch(Dispatchers.IO) {
            val audioTranscriptionBeanList = AudioTranscriptionDbHelper.findCorrespondBean(bean)
            if (audioTranscriptionBeanList.isEmpty()) {
                mState.value.audioBean = null
            } else {
                val audioTranscriptionBean = AudioTranscriptionBean(id = bean.id, path = bean.path, userId = bean.useId)
                val audioPhrase = mutableListOf<AudioTranscriptionViewModel.SpeakPhrase>()
                audioTranscriptionBeanList.forEachIndexed { _, transcribe ->
                    transcribe?.apply {
                        audioTranscriptionBean.focusSpeakerSummaryStr = this.focusSpeakerSummaryStr
                        audioTranscriptionBean.fileIdInCloud = this.fileIdInCloud
                        audioTranscriptionBean.transcriptionId = this.transcriptionId
                        audioTranscriptionBean.summaryTaskId = this.summaryTaskId
                        audioTranscriptionBean.summaryStr = this.summaryStr
                        audioTranscriptionBean.isDistinguishSpeakers = this.isDistinguishSpeakers
                        audioTranscriptionBean.summaryTitle = this.summaryTitle
                        audioTranscriptionBean.summaryTemplate = this.summaryTemplate
                        audioTranscriptionBean.summaryErrorCode = this.summaryErrorCode
                        if (!TextUtils.isEmpty(this.srcStr)) {
//                            Timber.d("audioTranscriptionBeanList srcStr:${this.srcStr}}")
                            val phrase = if (isJsonArray(this.srcStr)) {
//                                val list = gson.fromJson(this.srcStr, Array<Phrase>::class.java).toList()
//                                if (list.isNotEmpty() && index < list.size) {
//                                    list[index]
//                                } else {
//                                    null
//                                }
                                null
                            } else {
                                gson.fromJson(this.srcStr, Phrase::class.java)
                            }
                            if (phrase != null) {
                                val speakPhrase = AudioTranscriptionViewModel.SpeakPhrase(
                                    transcribe.objId,
                                    this.speakerName ?: "",
                                    phrase,
                                    transcribe.isFocusSpeaker
                                )
                                audioPhrase.add(speakPhrase)
                            }
                        }
                    }
                }
                Timber.d("loadAudioTranscriptionData called audioPhrase:${audioPhrase.size}")
                taskId.value = audioTranscriptionBean.transcriptionId ?: ""
                summaryTaskId.value = audioTranscriptionBean.summaryTaskId ?: ""
                transcriptionSummaryTitle.value = audioTranscriptionBean.summaryTitle ?: ""
                viewModel.summaryTemplate = audioTranscriptionBean.summaryTemplate ?: "abstractAutopilot"
                viewModel.summaryErrorCode = audioTranscriptionBean.summaryErrorCode
                viewModel.fillPhraseList(audioPhrase)
                Timber.d("loadAudioTranscriptionData called summary:${audioTranscriptionBean.summaryStr?.isNotEmpty()}")
                viewModel.fillSummary(audioTranscriptionBean.summaryStr ?: "")
                audioTranscriptionBean.srcStr = viewModel.transcribePhrases
                mState.value.audioBean = audioTranscriptionBean
            }
            sendInitializationEvent(requireContext(), viewLifecycleOwner)
        }
    }

    private fun isJsonArray(jsonString: String?): Boolean {
        if (jsonString.isNullOrBlank()) return false
        val trimmed = jsonString.trim()
        return trimmed.startsWith('[') && trimmed.endsWith(']')
    }

    private fun AudioTranscriptionViewModel.sendInitializationEvent(
        context: Context,
        lifecycleOwner: LifecycleOwner
    ) {
        sendEvent(
            AudioTranscriptionEvent.Init(
                context,
                lifecycleOwner = lifecycleOwner
            )
        )
    }

    override fun onDestroyView() {
        super.onDestroyView()
        viewModel.stopPlay()
        viewModel._hasShownTranslateDialog.value = false
    }

    override fun onPause() {
        super.onPause()
        // 重置录音的转写及总结状态
        currentMediaBean?.let { bean ->
            // resetRecordingStatus(bean)
        }
    }

    /**
     * 重置录音的转写及总结状态
     * 当用户进入录音详情时调用，清除之前的状态指示器
     * 只有当转写和总结都处于最终状态时才会清空，避免清除进行中的任务
     */
    @Suppress("TooGenericExceptionCaught")
    private fun resetRecordingStatus(mediaBean: MediaBean) {
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                Timber.i("检查录音状态是否可以重置: ${mediaBean.fileName}")

                // 检查是否可以安全清空状态（转写和总结都不在进行中）
                if (AudioTranscriptionDbHelper.canSafelyClearStatus(mediaBean)) {
                    Timber.i("录音状态可以安全重置，执行重置: ${mediaBean.fileName}")

                    // 重置转写状态为NONE
                    AudioTranscriptionDbHelper.updateTranscriptionStatus(mediaBean, TranscriptionStatus.NONE)

                    // 重置总结状态为NONE
                    AudioTranscriptionDbHelper.updateSummaryStatus(mediaBean, SummaryStatus.NONE)

                    Timber.i("成功重置录音状态: ${mediaBean.fileName}")
                } else {
                    Timber.i("录音有任务正在进行中，跳过状态重置: ${mediaBean.fileName}")

                    // 记录当前状态用于调试
                    val isTranscriptionInProgress = AudioTranscriptionDbHelper.isTranscriptionInProgress(mediaBean)
                    val isSummaryInProgress = AudioTranscriptionDbHelper.isSummaryInProgress(mediaBean)
                    Timber.i("转写进行中: $isTranscriptionInProgress, 总结进行中: $isSummaryInProgress")
                }
            } catch (e: Exception) {
                Timber.e(e, "检查或重置录音状态失败: ${mediaBean.fileName}")
            }
        }
    }

    override val contentView: @Composable () -> Unit =
        { AudioTranscriptionScreen(viewModel, navigator) }
}
