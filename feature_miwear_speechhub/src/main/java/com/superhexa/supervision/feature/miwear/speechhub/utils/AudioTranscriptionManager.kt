@file:Suppress("MagicN<PERSON>ber", "LongParameterList")

package com.superhexa.supervision.feature.miwear.speechhub.utils

import com.superhexa.supervision.feature.miwear.speechhub.service.TranscriptionStateListener
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.xiaomi.ai.capability.constant.Env
import com.xiaomi.ai.capability.constant.Language
import com.xiaomi.ai.capability.request.model.TransReqResponse
import com.xiaomi.ai.core.AivsConfig
import com.xiaomi.aivs.capability.AiCapabilityWrapper
import com.xiaomi.aivs.config.ConfigCache
import com.xiaomi.aivs.track.RecordEventParams
import com.xiaomi.aivs.track.SummaryEventParams
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.concurrent.ConcurrentHashMap

/**
 * 转写轮询管理器
 * 负责管理音频转写的请求发起、轮询获取结果等逻辑
 */
@Suppress("TooGenericExceptionCaught")
class AudioTranscriptionManager(
    private val coroutineScope: CoroutineScope
) {

    companion object {
        private const val POLLING_DELAY_MS = 3000L // 3秒轮询间隔
        private const val FAKE_TASK_ID_PREFIX = "fake_" // 临时taskId前缀
    }

    private val aiCapability = AiCapabilityWrapper.INSTANCE

    // 支持多任务的数据结构
    private val activeJobs = ConcurrentHashMap<String, Job>() // taskId -> Job
    private val taskContexts = ConcurrentHashMap<String, TaskContext>() // taskId -> TaskContext

    /**
     * 任务上下文信息
     */
    private data class TaskContext(
        val taskId: String,
        val filePath: String,
        val callback: TranscriptionStateListener,
        val isReTranscribe: Boolean = false
    )

    /**
     * 转写请求参数
     */
    data class TranscriptionRequest(
        val filePath: String,
        val language: String = Language.ZH_CN,
        val distinguishSpeakers: Boolean = false,
        val isReTranscribe: Boolean = false
    )

    /**
     * 发起转写请求
     */
    fun startTranscription(
        request: TranscriptionRequest,
        callback: TranscriptionStateListener
    ): String {
        Timber.i("开始转写请求: filePath=${request.filePath}, language=${request.language}")

        // 生成临时taskId
        val tempTaskId = generateFakeTaskId(request.filePath.hashCode().toString())

        // 检查该文件是否已经在转写中
        if (isFileTranscribing(request.filePath)) {
            Timber.w("文件已在转写中，忽略新请求: ${request.filePath}")
            return tempTaskId
        }

        // 保存任务上下文
        val context = TaskContext(
            taskId = tempTaskId,
            filePath = request.filePath,
            callback = callback,
            isReTranscribe = request.isReTranscribe
        )
        taskContexts[tempTaskId] = context

        callback.onPollingStatusChanged(tempTaskId, request.filePath, true)

        coroutineScope.launch(Dispatchers.IO) {
            try {
                aiCapability.getToken { token ->
                    executeTranscriptionRequest(request, token, callback, tempTaskId)
                }
            } catch (e: Exception) {
                Timber.e(e, "转写请求发生异常")
                coroutineScope.launch(Dispatchers.Main) {
                    callback.onTranscriptionFailed(tempTaskId, request.filePath, -1, e.message, request.isReTranscribe)
                    finishPolling(tempTaskId, callback)
                }
            }
        }

        return tempTaskId
    }

    /**
     * 使用已有taskId开始轮询结果
     */
    fun startPollingWithTaskId(
        taskId: String,
        filePath: String,
        callback: TranscriptionStateListener
    ) {
        Timber.i("开始使用已有taskId轮询: $taskId")

        // 检查该taskId是否已经在轮询中
        if (activeJobs.containsKey(taskId)) {
            Timber.w("taskId已在轮询中，忽略新请求: $taskId")
            return
        }

        // 保存任务上下文
        val context = TaskContext(
            taskId = taskId,
            filePath = filePath,
            callback = callback
        )
        taskContexts[taskId] = context

        callback.onPollingStatusChanged(taskId, filePath, true)
        callback.onTranscriptionStarted(taskId, filePath)

        coroutineScope.launch(Dispatchers.IO) {
            aiCapability.getToken { token ->
                coroutineScope.launch(Dispatchers.IO) {
                    startPollingResult(taskId, token, callback)
                }
            }
        }
    }

    /**
     * 停止当前的转写轮询
     */
    fun stopPolling() {
        Timber.i("停止所有转写轮询")
        activeJobs.values.forEach { job ->
            job.cancel()
        }
        activeJobs.clear()
        taskContexts.clear()
    }

    /**
     * 检查特定文件是否正在转写
     */
    fun isFileTranscribing(filePath: String): Boolean {
        return taskContexts.values.any { it.filePath == filePath }
    }

    /**
     * 处理文件上传中的情况
     */
    fun handleUploadingFile(
        filePath: String,
        callback: TranscriptionStateListener
    ): String {
        Timber.i("处理文件上传中的转写监听: $filePath")

        // 生成临时taskId
        val tempTaskId = generateFakeTaskId(filePath.hashCode().toString())

        // 检查该文件是否已经在转写中
        if (isFileTranscribing(filePath)) {
            Timber.w("文件已在转写中，忽略新请求: $filePath")
            return tempTaskId
        }

        // 保存任务上下文
        val context = TaskContext(
            taskId = tempTaskId,
            filePath = filePath,
            callback = callback
        )
        taskContexts[tempTaskId] = context

        callback.onPollingStatusChanged(tempTaskId, filePath, true)

        // 通知上传开始（文件已经在上传中）
        callback.onUploadStarted(tempTaskId, filePath)

        aiCapability.setTranscribeListener { result ->
            handleTranscribeRequestResult(result, callback, tempTaskId)
        }

        return tempTaskId
    }

    private fun executeTranscriptionRequest(
        request: TranscriptionRequest,
        token: String,
        callback: TranscriptionStateListener,
        taskId: String
    ) {
        Timber.i("执行转写请求: ${request.filePath}, taskId: $taskId")

        // 通知上传开始
        val context = taskContexts[taskId]
        if (context != null) {
            callback.onUploadStarted(taskId, context.filePath)
        }

        val language = if (request.language == Language.ZH_CN) Language.ZH_CN else Language.EN_US
        aiCapability.fastTranscribeRequest(
            mutableListOf(language),
            request.distinguishSpeakers,
            request.filePath,
            onResult = { result ->
                handleTranscribeRequestResult(result, callback, taskId)
            }
        )
    }

    private fun handleTranscribeRequestResult(
        result: Result<TransReqResponse>,
        callback: TranscriptionStateListener,
        tempTaskId: String
    ) {
        result.onSuccess { response ->
            val realTaskId = response.taskId
            Timber.i("转写请求成功: realTaskId=$realTaskId, tempTaskId=$tempTaskId")

            MMKVUtils.encode(RecordEventParams.EVENT_RECORD_UPLOAD_REQUESTID, response.requestId)
            MMKVUtils.encode(SummaryEventParams.EVENT_SUMMARY_UPLOAD_REQUESTID, response.requestId)

            // 更新任务上下文中的taskId
            val context = taskContexts.remove(tempTaskId)
            if (context != null) {
                // 通知上传成功，传递云端返回的真实taskId
                callback.onUploadSuccess(realTaskId, context.filePath, response.fileId)

                val updatedContext = context.copy(taskId = realTaskId)
                taskContexts[realTaskId] = updatedContext

                callback.onTranscriptionStarted(realTaskId, context.filePath)

                coroutineScope.launch(Dispatchers.IO) {
                    aiCapability.getToken { token ->
                        coroutineScope.launch(Dispatchers.IO) {
                            startPollingResult(realTaskId, token, callback)
                        }
                    }
                }
            }
        }.onFailure { error ->
            Timber.e("转写请求失败: ${error.message}")
            val errorCode = error.message?.toIntOrNull() ?: -1
            val context = taskContexts[tempTaskId]
            coroutineScope.launch(Dispatchers.Main) {
                // 通知上传失败
                if (context != null) {
                    callback.onUploadFailed(tempTaskId, context.filePath, errorCode, error.message)
                }
                callback.onTranscriptionFailed(
                    tempTaskId,
                    context?.filePath ?: "",
                    errorCode,
                    error.message,
                    context?.isReTranscribe ?: false
                )
                finishPolling(tempTaskId, callback)
            }
        }
    }

    @Suppress("LongMethod")
    private suspend fun startPollingResult(
        taskId: String,
        token: String,
        callback: TranscriptionStateListener
    ) {
        Timber.i("开始轮询转写结果: taskId=$taskId")

        val pollingJob = coroutineScope.launch(Dispatchers.IO) {
            var continuePolling = true

            while (continuePolling && isActive) {
                try {
                    aiCapability.fetchTranscribeResult(
                        taskId,
                        token,
                        onResult = { result ->
                            result.onSuccess { response ->
                                Timber.i("轮询获取转写结果成功: taskId=$taskId")
                                MMKVUtils.encode(
                                    RecordEventParams.EVENT_RECORD_TRANS_REQUESTID,
                                    response.requestId
                                )
                                continuePolling = false
                                val fileId = response.fileId ?: ""
                                val phrases = response.result?.phrases
                                val context = taskContexts[taskId]
                                val filePath = context?.filePath ?: ""

                                if (phrases.isNullOrEmpty()) {
                                    coroutineScope.launch(Dispatchers.Main) {
                                        callback.onTranscriptionFailed(
                                            taskId,
                                            filePath,
                                            204,
                                            "转写结果为空",
                                            context?.isReTranscribe ?: false
                                        )
                                        finishPolling(taskId, callback)
                                    }
                                } else {
                                    coroutineScope.launch(Dispatchers.Main) {
                                        callback.onTranscriptionSuccess(taskId, fileId, filePath, phrases)
                                        finishPolling(taskId, callback)
                                    }
                                }
                            }.onFailure { error ->
                                Timber.e("轮询获取转写结果失败: taskId=$taskId, ${error.message}")
                                continuePolling = false
                                val errorCode = error.message?.toIntOrNull() ?: -1
                                val context = taskContexts[taskId]
                                val filePath = context?.filePath ?: ""
                                coroutineScope.launch(Dispatchers.Main) {
                                    callback.onTranscriptionFailed(
                                        taskId,
                                        filePath,
                                        errorCode,
                                        error.message,
                                        context?.isReTranscribe ?: false
                                    )
                                    finishPolling(taskId, callback)
                                }
                            }
                        }
                    )

                    if (continuePolling) {
                        delay(POLLING_DELAY_MS)
                    }
                } catch (e: Exception) {
                    Timber.e(e, "轮询过程中发生异常: taskId=$taskId")
                    continuePolling = false
                    val context = taskContexts[taskId]
                    val filePath = context?.filePath ?: ""
                    coroutineScope.launch(Dispatchers.Main) {
                        callback.onTranscriptionFailed(taskId, filePath, -1, e.message, context?.isReTranscribe ?: false)
                        finishPolling(taskId, callback)
                    }
                }
            }
        }

        // 保存Job引用
        activeJobs[taskId] = pollingJob
    }

    private fun finishPolling(taskId: String, callback: TranscriptionStateListener) {
        val context = taskContexts.remove(taskId)
        activeJobs.remove(taskId)
        val filePath = context?.filePath ?: ""
        callback.onPollingStatusChanged(taskId, filePath, false)
        Timber.i("完成轮询任务: $taskId")
    }

    private fun getEnv(): Int {
        val envCache = ConfigCache.envDomain()
        val env = when (envCache) {
            AivsConfig.ENV_PREVIEW4TEST -> Env.P4T
            AivsConfig.ENV_PRODUCTION -> Env.PROD
            else -> Env.PREV
        }
        Timber.i("getEnv envCache $envCache, env $env")
        return env
    }

    /**
     * 判断taskId是否为临时ID
     */
    fun isFakeTaskId(taskId: String): Boolean {
        return taskId.startsWith(FAKE_TASK_ID_PREFIX)
    }

    /**
     * 生成临时taskId
     */
    fun generateFakeTaskId(md5sum: String): String {
        return "$FAKE_TASK_ID_PREFIX$md5sum"
    }

    /**
     * 释放资源
     */
    fun destroy() {
        Timber.i("销毁转写轮询管理器")
        stopPolling()
    }
}
